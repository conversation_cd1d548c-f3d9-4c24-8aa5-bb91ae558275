import { useEffect, useState } from 'react';
import { roleBasedNotificationService, setupNotificationPermissions, UserRole } from '@/utils/roleBasedNotifications';
import useCurrentUser from './useCurrentUser';
import * as Notifications from 'expo-notifications';

export const useRoleBasedNotifications = () => {
  const { data: currentUser } = useCurrentUser();
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasPermissions, setHasPermissions] = useState(false);

  // Initialize notification service with user context
  useEffect(() => {
    const initializeNotifications = async () => {
      if (!currentUser?.id) return;

      // Setup permissions
      const permissionsGranted = await setupNotificationPermissions();
      setHasPermissions(permissionsGranted);

      // Determine user role based on user data
      // You might need to adjust this logic based on your user data structure
      const userRole: UserRole = currentUser.isDriver || currentUser.carDetails ? 'driver' : 'passenger';

      // Set user context in notification service
      roleBasedNotificationService.setUserContext(userRole, currentUser.id);
      setIsInitialized(true);

      console.log(`🔔 Role-based notifications initialized for ${userRole}`);
    };

    initializeNotifications();
  }, [currentUser]);

  // Notification handlers for different scenarios
  const notificationHandlers = {
    // Driver notifications
    onRideRequest: (tripId: string, passengerName: string, pickupLocation: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyDriverRideRequest(tripId, passengerName, pickupLocation);
    },

    onTripStartForDriver: (tripId: string, passengerCount: number) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyDriverTripStart(tripId, passengerCount);
    },

    onPickupReminder: (tripId: string, passengerName: string, location: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyDriverPickupReminder(tripId, passengerName, location);
    },

    // Passenger notifications
    onBookingAccepted: (tripId: string, driverName: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyPassengerBookingAccepted(tripId, driverName);
    },

    onBookingDeclined: (tripId: string, driverName: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyPassengerBookingDeclined(tripId, driverName);
    },

    onTripStartForPassenger: (tripId: string, driverName: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyPassengerTripStart(tripId, driverName);
    },

    onDriverArriving: (tripId: string, driverName: string, eta: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyPassengerDriverArriving(tripId, driverName, eta);
    },

    // Shared notifications
    onTripCancelled: (tripId: string, cancelledBy: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyTripCancelled(tripId, cancelledBy);
    },

    onTripCompleted: (tripId: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyTripCompleted(tripId);
    },

    onEmergency: (tripId: string, message: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.notifyEmergency(tripId, message);
    },

    // Utility functions
    clearAll: () => {
      if (!isInitialized) return;
      return roleBasedNotificationService.clearAllNotifications();
    },

    clearByType: (type: string) => {
      if (!isInitialized) return;
      return roleBasedNotificationService.clearNotificationsByType(type);
    }
  };

  return {
    isInitialized,
    hasPermissions,
    currentUserRole: currentUser?.isDriver || currentUser?.carDetails ? 'driver' : 'passenger',
    ...notificationHandlers
  };
};

// Hook for listening to notification responses
export const useNotificationListener = () => {
  const [lastNotificationResponse, setLastNotificationResponse] = useState<Notifications.NotificationResponse | null>(null);

  useEffect(() => {
    const subscription = Notifications.addNotificationResponseReceivedListener(response => {
      setLastNotificationResponse(response);
      
      // Handle notification tap based on type
      const notificationType = response.notification.request.content.data?.type;
      const tripId = response.notification.request.content.data?.tripId;

      console.log(`📱 Notification tapped: ${notificationType} for trip ${tripId}`);

      // You can add navigation logic here based on notification type
      switch (notificationType) {
        case 'ride_request':
        case 'booking_accepted':
        case 'trip_started':
          // Navigate to trip details or appropriate screen
          break;
        case 'driver_arriving':
          // Navigate to trip tracking screen
          break;
        case 'trip_cancelled':
        case 'trip_completed':
          // Navigate to home or trip history
          break;
      }
    });

    return () => subscription.remove();
  }, []);

  return {
    lastNotificationResponse,
    clearLastResponse: () => setLastNotificationResponse(null)
  };
};

// Hook for notification badge management
export const useNotificationBadge = () => {
  const [badgeCount, setBadgeCount] = useState(0);

  const updateBadge = async (count: number) => {
    try {
      await Notifications.setBadgeCountAsync(count);
      setBadgeCount(count);
    } catch (error) {
      console.error('Error updating badge count:', error);
    }
  };

  const incrementBadge = () => updateBadge(badgeCount + 1);
  const decrementBadge = () => updateBadge(Math.max(0, badgeCount - 1));
  const clearBadge = () => updateBadge(0);

  return {
    badgeCount,
    updateBadge,
    incrementBadge,
    decrementBadge,
    clearBadge
  };
};
