import * as Notifications from 'expo-notifications';
import { roleBasedNotificationService } from './roleBasedNotifications';

interface ScheduledNotification {
  id: string;
  tripId: string;
  type: 'reminder_30min' | 'trip_start';
  scheduledTime: Date;
  notificationId?: string;
}

class TimeBasedNotificationService {
  private scheduledNotifications: Map<string, ScheduledNotification> = new Map();

  // Schedule notifications for a trip
  async scheduleTrip(tripId: string, tripStartTime: Date, tripDetails: {
    origin: string;
    destination: string;
    driverName?: string;
    passengerCount?: number;
  }) {
    const now = new Date();
    const startTime = new Date(tripStartTime);
    
    // Only schedule if trip is more than 3 minutes in the future
    const timeDifference = startTime.getTime() - now.getTime();
    const threeMinutes = 3 * 60 * 1000;
    
    if (timeDifference <= threeMinutes) {
      console.log(`⏰ Trip ${tripId} starts too soon (${Math.round(timeDifference / 1000)}s), skipping time-based notifications`);
      return;
    }

    console.log(`⏰ Scheduling time-based notifications for trip ${tripId}`);

    // Schedule 30-minute reminder
    await this.schedule30MinuteReminder(tripId, startTime, tripDetails);

    // Schedule trip start notification
    await this.scheduleTripStartNotification(tripId, startTime, tripDetails);
  }

  // Schedule 30-minute reminder
  private async schedule30MinuteReminder(tripId: string, tripStartTime: Date, tripDetails: any) {
    const reminderTime = new Date(tripStartTime.getTime() - 30 * 60 * 1000); // 30 minutes before
    const now = new Date();

    if (reminderTime <= now) {
      console.log(`⏰ 30-minute reminder for trip ${tripId} is in the past, skipping`);
      return;
    }

    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Trip Reminder',
          body: `Your trip from ${tripDetails.origin} to ${tripDetails.destination} starts in 30 minutes`,
          data: {
            type: 'reminder_30min',
            tripId,
            tripDetails
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: reminderTime,
        },
      });

      const scheduledNotification: ScheduledNotification = {
        id: `reminder_30min_${tripId}`,
        tripId,
        type: 'reminder_30min',
        scheduledTime: reminderTime,
        notificationId
      };

      this.scheduledNotifications.set(scheduledNotification.id, scheduledNotification);
      console.log(`⏰ Scheduled 30-minute reminder for trip ${tripId} at ${reminderTime.toLocaleString()}`);
    } catch (error) {
      console.error('Error scheduling 30-minute reminder:', error);
    }
  }

  // Schedule trip start notification
  private async scheduleTripStartNotification(tripId: string, tripStartTime: Date, tripDetails: any) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: 'Trip Starting Now',
          body: `Your trip from ${tripDetails.origin} to ${tripDetails.destination} is starting now!`,
          data: {
            type: 'trip_start',
            tripId,
            tripDetails
          },
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: tripStartTime,
        },
      });

      const scheduledNotification: ScheduledNotification = {
        id: `trip_start_${tripId}`,
        tripId,
        type: 'trip_start',
        scheduledTime: tripStartTime,
        notificationId
      };

      this.scheduledNotifications.set(scheduledNotification.id, scheduledNotification);
      console.log(`⏰ Scheduled trip start notification for trip ${tripId} at ${tripStartTime.toLocaleString()}`);
    } catch (error) {
      console.error('Error scheduling trip start notification:', error);
    }
  }

  // Cancel all notifications for a trip
  async cancelTripNotifications(tripId: string) {
    const notificationsToCancel = Array.from(this.scheduledNotifications.values())
      .filter(notification => notification.tripId === tripId);

    for (const notification of notificationsToCancel) {
      if (notification.notificationId) {
        try {
          await Notifications.cancelScheduledNotificationAsync(notification.notificationId);
          console.log(`⏰ Cancelled notification ${notification.id} for trip ${tripId}`);
        } catch (error) {
          console.error(`Error cancelling notification ${notification.id}:`, error);
        }
      }
      this.scheduledNotifications.delete(notification.id);
    }

    console.log(`⏰ Cancelled ${notificationsToCancel.length} notifications for trip ${tripId}`);
  }

  // Update trip notifications (cancel old, schedule new)
  async updateTripNotifications(tripId: string, newTripStartTime: Date, tripDetails: any) {
    await this.cancelTripNotifications(tripId);
    await this.scheduleTrip(tripId, newTripStartTime, tripDetails);
  }

  // Get scheduled notifications for a trip
  getTripNotifications(tripId: string): ScheduledNotification[] {
    return Array.from(this.scheduledNotifications.values())
      .filter(notification => notification.tripId === tripId);
  }

  // Get all scheduled notifications
  getAllScheduledNotifications(): ScheduledNotification[] {
    return Array.from(this.scheduledNotifications.values());
  }

  // Clean up expired notifications
  async cleanupExpiredNotifications() {
    const now = new Date();
    const expiredNotifications = Array.from(this.scheduledNotifications.values())
      .filter(notification => notification.scheduledTime <= now);

    for (const notification of expiredNotifications) {
      this.scheduledNotifications.delete(notification.id);
    }

    if (expiredNotifications.length > 0) {
      console.log(`⏰ Cleaned up ${expiredNotifications.length} expired notifications`);
    }
  }

  // Cancel all scheduled notifications
  async cancelAllNotifications() {
    const allNotifications = Array.from(this.scheduledNotifications.values());

    for (const notification of allNotifications) {
      if (notification.notificationId) {
        try {
          await Notifications.cancelScheduledNotificationAsync(notification.notificationId);
        } catch (error) {
          console.error(`Error cancelling notification ${notification.id}:`, error);
        }
      }
    }

    this.scheduledNotifications.clear();
    console.log(`⏰ Cancelled all ${allNotifications.length} scheduled notifications`);
  }

  // Check if notifications are scheduled for a trip
  hasTripNotifications(tripId: string): boolean {
    return Array.from(this.scheduledNotifications.values())
      .some(notification => notification.tripId === tripId);
  }

  // Get time until next notification for a trip
  getTimeUntilNextNotification(tripId: string): number | null {
    const tripNotifications = this.getTripNotifications(tripId);
    if (tripNotifications.length === 0) return null;

    const now = new Date();
    const nextNotification = tripNotifications
      .filter(notification => notification.scheduledTime > now)
      .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime())[0];

    if (!nextNotification) return null;

    return nextNotification.scheduledTime.getTime() - now.getTime();
  }

  // Format time until notification
  formatTimeUntilNotification(tripId: string): string | null {
    const timeUntil = this.getTimeUntilNextNotification(tripId);
    if (!timeUntil) return null;

    const minutes = Math.floor(timeUntil / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else {
      return `${minutes}m`;
    }
  }
}

// Export singleton instance
export const timeBasedNotificationService = new TimeBasedNotificationService();

// Helper function to schedule notifications when a trip is created
export const scheduleNotificationsForTrip = async (trip: {
  id: string;
  timestamp: string;
  origin: { name: string };
  destination: { name: string };
  driver?: { firstName: string; lastName: string };
  noOfPassengers?: number;
}) => {
  const tripStartTime = new Date(trip.timestamp);
  const tripDetails = {
    origin: trip.origin.name,
    destination: trip.destination.name,
    driverName: trip.driver ? `${trip.driver.firstName} ${trip.driver.lastName}` : undefined,
    passengerCount: trip.noOfPassengers
  };

  await timeBasedNotificationService.scheduleTrip(trip.id, tripStartTime, tripDetails);
};

// Helper function to cancel notifications when a trip is cancelled
export const cancelNotificationsForTrip = async (tripId: string) => {
  await timeBasedNotificationService.cancelTripNotifications(tripId);
};
