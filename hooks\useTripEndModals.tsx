import { useState, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { services } from '@/services';
import Toast from 'react-native-toast-message';
import { router } from 'expo-router';
import BottomSheet from '@gorhom/bottom-sheet';
import { realtimeNotificationHelpers } from '@/utils/realtimeNotifications';
import { useRide } from '@/context/RideProvider';
import { useTripStore } from '@/app/store/tripStore';
import { useTimeBasedNotifications } from './useTimeBasedNotifications';
import useCurrentUser from './useCurrentUser';

export const useTripEndModals = (tripId?: string, userRole: 'driver' | 'passenger' = 'driver') => {
  const queryClient = useQueryClient();
  const { data: currentUser } = useCurrentUser();
  const { resetAllState } = useRide();
  const { resetTripState } = useTripStore();
  const timeNotifications = useTimeBasedNotifications();

  // Modal states
  const [showEndTripModal, setShowEndTripModal] = useState(false);
  const [showEndTripOptions, setShowEndTripOptions] = useState(false);
  const [showEndTripEarly, setShowEndTripEarly] = useState(false);
  const [showPassengerEndTripModal, setShowPassengerEndTripModal] = useState(false);
  const [reportIssue, setReportIssue] = useState(false);
  const [somethingElse, setSomethingElse] = useState(false);

  // Option states
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [selectedIssue, setSelectedIssue] = useState<string>('');
  const [customIssueText, setCustomIssueText] = useState('');

  // Modal refs
  const endTripModalRef = useRef<BottomSheet>(null);

  // End trip mutation
  const { mutate: endTrip, isPending: isEndingTrip } = useMutation({
    mutationFn: services.endTrip,
    onSuccess: async (data) => {
      Toast.show({
        type: "success",
        text1: data?.message || "Trip ended successfully",
      });

      // Cancel time-based notifications for the trip
      if (tripId && timeNotifications.isInitialized) {
        await timeNotifications.cancelTrip(tripId);
      }

      // Reset all form states after trip ends
      resetAllState();
      resetTripState();

      // Send realtime notification to passengers
      if (tripId && currentUser?.firstName) {
        realtimeNotificationHelpers.notifyTripEnded(tripId, currentUser.firstName);
      }

      // Show appropriate modal based on user role
      if (userRole === 'driver') {
        setShowEndTripModal(false);
        setShowPassengerEndTripModal(true);
      } else {
        setShowPassengerEndTripModal(true);
      }

      // Invalidate all relevant queries to refresh the home screen
      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["requests"] });
      queryClient.invalidateQueries({ queryKey: ["trips"] });

      // Navigate to home after a delay
      setTimeout(() => {
        router.replace("/(tabs)/Home");
      }, 2000);
    },
    onError: (error) => {
      console.error('Error ending trip:', error);
      Toast.show({
        type: "error",
        text1: "Failed to end trip",
        text2: "Please try again",
      });
    }
  });

  // Handle end trip button click
  const handleEndTripClick = () => {
    if (userRole === 'driver') {
      setShowEndTripModal(true);
      setTimeout(() => {
        endTripModalRef.current?.snapToIndex(0);
      }, 100);
    } else {
      // For passengers, directly end the trip
      if (tripId) {
        endTrip({ dataBody: { tripId } });
      }
    }
  };

  // Handle trip completion (normal flow)
  const handleTripCompletion = () => {
    if (tripId) {
      endTrip({ dataBody: { tripId } });
    }
  };

  // Handle early trip end
  const handleEarlyTripEnd = (reason: string) => {
    setSelectedOption(reason);
    setShowEndTripOptions(false);
    setShowEndTripEarly(true);
    
    // End the trip with the selected reason
    if (tripId) {
      endTrip({ 
        dataBody: { 
          tripId,
          endReason: reason,
          customReason: reason === 'custom' ? customIssueText : undefined
        } 
      });
    }
  };

  // Handle issue reporting
  const handleIssueReport = (issue: string) => {
    setSelectedIssue(issue);
    setReportIssue(false);
    setShowEndTripEarly(true);
    
    // End the trip with the reported issue
    if (tripId) {
      endTrip({ 
        dataBody: { 
          tripId,
          endReason: 'issue',
          issue: issue,
          customIssue: issue === 'custom' ? customIssueText : undefined
        } 
      });
    }
  };

  // Close all modals
  const closeAllModals = () => {
    setShowEndTripModal(false);
    setShowEndTripOptions(false);
    setShowEndTripEarly(false);
    setShowPassengerEndTripModal(false);
    setReportIssue(false);
    setSomethingElse(false);
    setSelectedOption('');
    setSelectedIssue('');
    setCustomIssueText('');
    endTripModalRef.current?.close();
  };

  // Reset modal states
  const resetModalStates = () => {
    closeAllModals();
  };

  return {
    // Modal states
    showEndTripModal,
    setShowEndTripModal,
    showEndTripOptions,
    setShowEndTripOptions,
    showEndTripEarly,
    setShowEndTripEarly,
    showPassengerEndTripModal,
    setShowPassengerEndTripModal,
    reportIssue,
    setReportIssue,
    somethingElse,
    setSomethingElse,

    // Option states
    selectedOption,
    setSelectedOption,
    selectedIssue,
    setSelectedIssue,
    customIssueText,
    setCustomIssueText,

    // Modal ref
    endTripModalRef,

    // Actions
    handleEndTripClick,
    handleTripCompletion,
    handleEarlyTripEnd,
    handleIssueReport,
    closeAllModals,
    resetModalStates,

    // Mutation state
    isEndingTrip,

    // Utility
    userRole,
    isDriver: userRole === 'driver',
    isPassenger: userRole === 'passenger'
  };
};
