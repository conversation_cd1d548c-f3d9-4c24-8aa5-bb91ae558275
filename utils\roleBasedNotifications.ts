import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

export type UserRole = 'driver' | 'passenger';
export type NotificationTarget = 'driver' | 'passenger' | 'all';

export interface RoleBasedNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  targetRole: NotificationTarget;
  priority?: 'low' | 'normal' | 'high';
  sound?: boolean;
  vibrate?: boolean;
  badge?: number;
}

class RoleBasedNotificationService {
  private currentUserRole: UserRole | null = null;
  private currentUserId: string | null = null;

  // Set current user role and ID
  setUserContext(role: UserRole, userId: string) {
    this.currentUserRole = role;
    this.currentUserId = userId;
    console.log(`🔔 Notification service initialized for ${role} (${userId})`);
  }

  // Check if notification should be shown to current user
  shouldShowNotification(notification: RoleBasedNotification): boolean {
    if (!this.currentUserRole) {
      console.warn('User role not set, showing notification by default');
      return true;
    }

    // Show if target is 'all' or matches current user role
    const shouldShow = notification.targetRole === 'all' || notification.targetRole === this.currentUserRole;
    
    if (!shouldShow) {
      console.log(`🚫 Filtering out ${notification.targetRole} notification for ${this.currentUserRole}`);
    }

    return shouldShow;
  }

  // Send local notification with role filtering
  async sendLocalNotification(notification: RoleBasedNotification): Promise<boolean> {
    if (!this.shouldShowNotification(notification)) {
      return false;
    }

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: notification.title,
          body: notification.body,
          data: notification.data || {},
          sound: notification.sound !== false,
          badge: notification.badge,
          priority: this.mapPriority(notification.priority),
        },
        trigger: null, // Show immediately
      });

      console.log(`✅ Local notification sent to ${this.currentUserRole}: ${notification.title}`);
      return true;
    } catch (error) {
      console.error('Error sending local notification:', error);
      return false;
    }
  }

  // Map priority to platform-specific values
  private mapPriority(priority?: 'low' | 'normal' | 'high'): Notifications.AndroidNotificationPriority {
    switch (priority) {
      case 'low':
        return Notifications.AndroidNotificationPriority.LOW;
      case 'high':
        return Notifications.AndroidNotificationPriority.HIGH;
      default:
        return Notifications.AndroidNotificationPriority.DEFAULT;
    }
  }

  // Driver-specific notifications
  async notifyDriverRideRequest(tripId: string, passengerName: string, pickupLocation: string) {
    return this.sendLocalNotification({
      id: `ride-request-${tripId}-${Date.now()}`,
      title: 'New Ride Request',
      body: `${passengerName} wants to join your trip from ${pickupLocation}`,
      data: { type: 'ride_request', tripId, passengerName },
      targetRole: 'driver',
      priority: 'high',
      sound: true,
    });
  }

  async notifyDriverTripStart(tripId: string, passengerCount: number) {
    return this.sendLocalNotification({
      id: `trip-start-driver-${tripId}`,
      title: 'Trip Started',
      body: `Your trip has started with ${passengerCount} passenger(s)`,
      data: { type: 'trip_started', tripId, role: 'driver' },
      targetRole: 'driver',
      priority: 'normal',
    });
  }

  async notifyDriverPickupReminder(tripId: string, passengerName: string, location: string) {
    return this.sendLocalNotification({
      id: `pickup-reminder-${tripId}`,
      title: 'Pickup Reminder',
      body: `Time to pick up ${passengerName} at ${location}`,
      data: { type: 'pickup_reminder', tripId, passengerName },
      targetRole: 'driver',
      priority: 'high',
      sound: true,
    });
  }

  // Passenger-specific notifications
  async notifyPassengerBookingAccepted(tripId: string, driverName: string) {
    return this.sendLocalNotification({
      id: `booking-accepted-${tripId}`,
      title: 'Ride Request Accepted',
      body: `${driverName} accepted your ride request!`,
      data: { type: 'booking_accepted', tripId, driverName },
      targetRole: 'passenger',
      priority: 'high',
      sound: true,
    });
  }

  async notifyPassengerBookingDeclined(tripId: string, driverName: string) {
    return this.sendLocalNotification({
      id: `booking-declined-${tripId}`,
      title: 'Ride Request Declined',
      body: `${driverName} declined your ride request`,
      data: { type: 'booking_declined', tripId, driverName },
      targetRole: 'passenger',
      priority: 'normal',
    });
  }

  async notifyPassengerTripStart(tripId: string, driverName: string) {
    return this.sendLocalNotification({
      id: `trip-start-passenger-${tripId}`,
      title: 'Trip Started',
      body: `Your trip with ${driverName} has started`,
      data: { type: 'trip_started', tripId, role: 'passenger' },
      targetRole: 'passenger',
      priority: 'normal',
    });
  }

  async notifyPassengerDriverArriving(tripId: string, driverName: string, eta: string) {
    return this.sendLocalNotification({
      id: `driver-arriving-${tripId}`,
      title: 'Driver Arriving',
      body: `${driverName} will arrive in ${eta}`,
      data: { type: 'driver_arriving', tripId, driverName },
      targetRole: 'passenger',
      priority: 'high',
      sound: true,
    });
  }

  // Shared notifications (both roles)
  async notifyTripCancelled(tripId: string, cancelledBy: string) {
    return this.sendLocalNotification({
      id: `trip-cancelled-${tripId}`,
      title: 'Trip Cancelled',
      body: `Trip has been cancelled by ${cancelledBy}`,
      data: { type: 'trip_cancelled', tripId, cancelledBy },
      targetRole: 'all',
      priority: 'high',
      sound: true,
    });
  }

  async notifyTripCompleted(tripId: string) {
    return this.sendLocalNotification({
      id: `trip-completed-${tripId}`,
      title: 'Trip Completed',
      body: 'Your trip has been completed successfully',
      data: { type: 'trip_completed', tripId },
      targetRole: 'all',
      priority: 'normal',
    });
  }

  // Emergency notifications
  async notifyEmergency(tripId: string, message: string) {
    return this.sendLocalNotification({
      id: `emergency-${tripId}-${Date.now()}`,
      title: 'Emergency Alert',
      body: message,
      data: { type: 'emergency', tripId },
      targetRole: 'all',
      priority: 'high',
      sound: true,
      vibrate: true,
    });
  }

  // Clear all notifications
  async clearAllNotifications() {
    await Notifications.dismissAllNotificationsAsync();
    console.log('🧹 All notifications cleared');
  }

  // Clear notifications by type
  async clearNotificationsByType(type: string) {
    const notifications = await Notifications.getPresentedNotificationsAsync();
    const notificationsToCancel = notifications
      .filter(notification => notification.request.content.data?.type === type)
      .map(notification => notification.request.identifier);

    await Promise.all(
      notificationsToCancel.map(id => Notifications.dismissNotificationAsync(id))
    );

    console.log(`🧹 Cleared ${notificationsToCancel.length} notifications of type: ${type}`);
  }
}

// Export singleton instance
export const roleBasedNotificationService = new RoleBasedNotificationService();

// Helper function to setup notification permissions
export const setupNotificationPermissions = async (): Promise<boolean> => {
  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.warn('Notification permissions not granted');
      return false;
    }

    // Configure notification behavior
    await Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });

    console.log('✅ Notification permissions granted and configured');
    return true;
  } catch (error) {
    console.error('Error setting up notification permissions:', error);
    return false;
  }
};
