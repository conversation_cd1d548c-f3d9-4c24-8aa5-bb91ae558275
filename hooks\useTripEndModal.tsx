import { useEffect, useState, useRef } from 'react';
import { tripEndModalService, TripEndModalData } from '@/utils/tripEndModalService';
import useCurrentUser from './useCurrentUser';
import BottomSheet from '@gorhom/bottom-sheet';

export const useTripEndModal = () => {
  const { data: currentUser } = useCurrentUser();
  const [activeModal, setActiveModal] = useState<TripEndModalData | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const modalRef = useRef<BottomSheet>(null);

  useEffect(() => {
    // Register callbacks with the trip end modal service
    tripEndModalService.registerCallbacks({
      onModalShow: (data: TripEndModalData) => {
        console.log('🎭 Trip end modal show callback triggered', data);
        setActiveModal(data);
        setIsVisible(true);
        
        // Open the bottom sheet
        setTimeout(() => {
          modalRef.current?.snapToIndex(0);
        }, 100);
      },
      
      onModalClose: (tripId: string) => {
        console.log('🎭 Trip end modal close callback triggered', tripId);
        setIsVisible(false);
        setActiveModal(null);
        
        // Close the bottom sheet
        modalRef.current?.close();
      },
      
      onRatingSubmit: (tripId: string, rating: number) => {
        console.log(`⭐ Rating submitted: ${rating} stars for trip ${tripId}`);
        // Handle rating submission logic here
      },
      
      onPaymentComplete: (tripId: string) => {
        console.log(`💳 Payment completed for trip ${tripId}`);
        // Handle payment completion logic here
      }
    });

    // Cleanup function
    return () => {
      tripEndModalService.clearAllModals();
    };
  }, []);

  // Manual modal controls
  const showModal = (data: TripEndModalData) => {
    tripEndModalService.showTripEndModal(data);
  };

  const closeModal = (tripId?: string) => {
    if (tripId) {
      tripEndModalService.closeTripEndModal(tripId);
    } else if (activeModal) {
      tripEndModalService.closeTripEndModal(activeModal.tripId);
    }
  };

  const submitRating = (rating: number) => {
    if (activeModal) {
      tripEndModalService.submitRating(activeModal.tripId, rating);
    }
  };

  const completePayment = () => {
    if (activeModal) {
      tripEndModalService.completePayment(activeModal.tripId);
    }
  };

  return {
    // Modal state
    activeModal,
    isVisible,
    modalRef,
    
    // Modal controls
    showModal,
    closeModal,
    submitRating,
    completePayment,
    
    // Helper functions
    isDriver: activeModal?.userRole === 'driver',
    isPassenger: activeModal?.userRole === 'passenger',
    shouldShowRating: activeModal?.showRating || false,
    shouldShowPayment: activeModal?.showPayment || false,
    
    // Trip details
    tripDetails: activeModal?.tripDetails,
    endReason: activeModal?.endReason
  };
};

// Hook for integrating with existing modal components
export const useTripEndModalIntegration = () => {
  const tripEndModal = useTripEndModal();
  const [rating, setRating] = useState(0);

  // Handle rating change
  const handleRatingChange = (newRating: number) => {
    setRating(newRating);
  };

  // Handle rating submission
  const handleRatingSubmit = () => {
    if (rating > 0) {
      tripEndModal.submitRating(rating);
      setRating(0); // Reset rating
    }
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    tripEndModal.completePayment();
  };

  // Handle modal close
  const handleModalClose = () => {
    tripEndModal.closeModal();
    setRating(0); // Reset rating when closing
  };

  // Generate modal content based on end reason
  const getModalContent = () => {
    if (!tripEndModal.activeModal) return null;

    const { endReason, tripDetails, userRole } = tripEndModal.activeModal;

    switch (endReason) {
      case 'completed':
        return {
          title: userRole === 'driver' ? 'Trip Completed!' : 'Trip Ended',
          emoji: '🎉',
          message: userRole === 'driver' 
            ? 'You have successfully completed the trip!'
            : 'We hope you enjoyed your trip!',
          showRating: userRole === 'passenger',
          showPayment: userRole === 'passenger'
        };
        
      case 'cancelled':
        return {
          title: 'Trip Cancelled',
          emoji: '❌',
          message: 'The trip has been cancelled.',
          showRating: false,
          showPayment: false
        };
        
      case 'early':
        return {
          title: 'Trip Ended Early',
          emoji: '⏰',
          message: 'The trip ended earlier than expected.',
          showRating: userRole === 'passenger',
          showPayment: userRole === 'passenger'
        };
        
      default:
        return {
          title: 'Trip Update',
          emoji: 'ℹ️',
          message: 'Trip status has been updated.',
          showRating: false,
          showPayment: false
        };
    }
  };

  return {
    ...tripEndModal,
    
    // Rating state
    rating,
    handleRatingChange,
    handleRatingSubmit,
    
    // Event handlers
    handlePaymentComplete,
    handleModalClose,
    
    // Content generation
    modalContent: getModalContent(),
    
    // Utility functions
    canSubmitRating: rating > 0,
    hasActiveTrip: !!tripEndModal.activeModal
  };
};
