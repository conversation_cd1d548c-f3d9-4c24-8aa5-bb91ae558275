import { useEffect, useState } from 'react';
import { timeBasedNotificationService, scheduleNotificationsForTrip, cancelNotificationsForTrip } from '@/utils/timeBasedNotifications';
import { AppState, AppStateStatus } from 'react-native';

export const useTimeBasedNotifications = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize the service
  useEffect(() => {
    setIsInitialized(true);
    
    // Clean up expired notifications on app start
    timeBasedNotificationService.cleanupExpiredNotifications();

    // Set up app state listener to clean up when app becomes active
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        timeBasedNotificationService.cleanupExpiredNotifications();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Schedule notifications for a trip
  const scheduleTrip = async (trip: {
    id: string;
    timestamp: string;
    origin: { name: string };
    destination: { name: string };
    driver?: { firstName: string; lastName: string };
    noOfPassengers?: number;
  }) => {
    if (!isInitialized) {
      console.warn('Time-based notification service not initialized');
      return false;
    }

    try {
      await scheduleNotificationsForTrip(trip);
      return true;
    } catch (error) {
      console.error('Error scheduling trip notifications:', error);
      return false;
    }
  };

  // Cancel notifications for a trip
  const cancelTrip = async (tripId: string) => {
    if (!isInitialized) {
      console.warn('Time-based notification service not initialized');
      return false;
    }

    try {
      await cancelNotificationsForTrip(tripId);
      return true;
    } catch (error) {
      console.error('Error cancelling trip notifications:', error);
      return false;
    }
  };

  // Update trip notifications
  const updateTrip = async (tripId: string, newTripStartTime: Date, tripDetails: any) => {
    if (!isInitialized) {
      console.warn('Time-based notification service not initialized');
      return false;
    }

    try {
      await timeBasedNotificationService.updateTripNotifications(tripId, newTripStartTime, tripDetails);
      return true;
    } catch (error) {
      console.error('Error updating trip notifications:', error);
      return false;
    }
  };

  // Get trip notification info
  const getTripInfo = (tripId: string) => {
    if (!isInitialized) return null;

    return {
      hasNotifications: timeBasedNotificationService.hasTripNotifications(tripId),
      notifications: timeBasedNotificationService.getTripNotifications(tripId),
      timeUntilNext: timeBasedNotificationService.getTimeUntilNextNotification(tripId),
      formattedTimeUntilNext: timeBasedNotificationService.formatTimeUntilNotification(tripId)
    };
  };

  // Get all scheduled notifications
  const getAllNotifications = () => {
    if (!isInitialized) return [];
    return timeBasedNotificationService.getAllScheduledNotifications();
  };

  // Cancel all notifications
  const cancelAll = async () => {
    if (!isInitialized) return false;

    try {
      await timeBasedNotificationService.cancelAllNotifications();
      return true;
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
      return false;
    }
  };

  return {
    isInitialized,
    scheduleTrip,
    cancelTrip,
    updateTrip,
    getTripInfo,
    getAllNotifications,
    cancelAll
  };
};

// Hook for automatic trip notification management
export const useAutoTripNotifications = (activeTrip?: any) => {
  const timeNotifications = useTimeBasedNotifications();
  const [lastScheduledTripId, setLastScheduledTripId] = useState<string | null>(null);

  useEffect(() => {
    if (!timeNotifications.isInitialized || !activeTrip?.id) return;

    // If we have a new active trip, schedule notifications
    if (activeTrip.id !== lastScheduledTripId) {
      const scheduleNotifications = async () => {
        const success = await timeNotifications.scheduleTrip({
          id: activeTrip.id,
          timestamp: activeTrip.timestamp,
          origin: activeTrip.origin,
          destination: activeTrip.destination,
          driver: activeTrip.driver,
          noOfPassengers: activeTrip.noOfPassengers
        });

        if (success) {
          setLastScheduledTripId(activeTrip.id);
          console.log(`⏰ Auto-scheduled notifications for trip ${activeTrip.id}`);
        }
      };

      scheduleNotifications();
    }

    // Cleanup function to cancel notifications if trip is removed
    return () => {
      if (lastScheduledTripId && lastScheduledTripId !== activeTrip?.id) {
        timeNotifications.cancelTrip(lastScheduledTripId);
        console.log(`⏰ Auto-cancelled notifications for trip ${lastScheduledTripId}`);
      }
    };
  }, [activeTrip?.id, timeNotifications.isInitialized, lastScheduledTripId]);

  return {
    ...timeNotifications,
    lastScheduledTripId,
    tripInfo: activeTrip?.id ? timeNotifications.getTripInfo(activeTrip.id) : null
  };
};

// Hook for trip countdown display
export const useTripCountdown = (tripId?: string, tripStartTime?: string) => {
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null);
  const [formattedTime, setFormattedTime] = useState<string | null>(null);
  const [isStartTime, setIsStartTime] = useState(false);

  useEffect(() => {
    if (!tripStartTime) return;

    const updateCountdown = () => {
      const now = new Date();
      const startTime = new Date(tripStartTime);
      const timeDiff = startTime.getTime() - now.getTime();

      setTimeRemaining(timeDiff);
      setIsStartTime(timeDiff <= 0);

      if (timeDiff > 0) {
        const hours = Math.floor(timeDiff / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        if (hours > 0) {
          setFormattedTime(`${hours}h ${minutes}m`);
        } else if (minutes > 0) {
          setFormattedTime(`${minutes}m ${seconds}s`);
        } else {
          setFormattedTime(`${seconds}s`);
        }
      } else {
        setFormattedTime('Now');
      }
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [tripStartTime]);

  return {
    timeRemaining,
    formattedTime,
    isStartTime,
    hasStarted: isStartTime
  };
};
