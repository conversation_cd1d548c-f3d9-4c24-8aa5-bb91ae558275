import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { pollingService } from '@/utils/pollingService';

interface SmartPollingOptions {
  queryKey: string[];
  queryFn: () => Promise<any>;
  enabled?: boolean;
  activeInterval?: number; // Interval when app is active (default: 10 seconds)
  backgroundInterval?: number; // Interval when app is in background (default: 30 seconds)
  staleTime?: number;
  onDataChange?: (newData: any, oldData: any) => void;
}

export const useSmartPolling = ({
  queryKey,
  queryFn,
  enabled = true,
  activeInterval = 10000, // 10 seconds
  backgroundInterval = 30000, // 30 seconds
  staleTime = 0,
  onDataChange
}: SmartPollingOptions) => {
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);
  const [currentInterval, setCurrentInterval] = useState(activeInterval);

  // Monitor app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      setAppState(nextAppState);
      
      // Adjust polling interval based on app state
      if (nextAppState === 'active') {
        setCurrentInterval(activeInterval);
      } else if (nextAppState === 'background') {
        setCurrentInterval(backgroundInterval);
      } else {
        // App is inactive, stop polling
        setCurrentInterval(0);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [activeInterval, backgroundInterval]);

  // Use React Query with dynamic interval
  const query = useQuery({
    queryKey,
    queryFn,
    enabled: enabled && currentInterval > 0,
    refetchInterval: currentInterval,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    staleTime,
    // Always consider data stale for real-time needs
    gcTime: 5 * 60 * 1000, // 5 minutes
  });

  // Track data changes and integrate with polling service
  useEffect(() => {
    if (query.data) {
      const queryKeyString = queryKey.join('-');
      const hasChanged = pollingService.checkForChanges(queryKeyString, query.data);

      if (hasChanged && onDataChange) {
        onDataChange(query.data, null);
      }
    }
  }, [query.data, queryKey, onDataChange]);

  return {
    ...query,
    isPolling: enabled && currentInterval > 0,
    currentInterval,
    appState
  };
};

// Specialized hooks for common use cases
export const useTripPolling = (tripId?: string, enabled: boolean = true) => {
  return useSmartPolling({
    queryKey: ['trip', tripId],
    queryFn: async () => {
      if (!tripId) return null;
      // Import services dynamically to avoid circular dependencies
      const { services } = await import('@/services');
      return services.getTrip(tripId);
    },
    enabled: enabled && !!tripId,
    activeInterval: 5000, // 5 seconds for active trips
    backgroundInterval: 15000, // 15 seconds in background
  });
};

export const useActiveTripPolling = (enabled: boolean = true) => {
  return useSmartPolling({
    queryKey: ['activeTrip'],
    queryFn: async () => {
      const { services } = await import('@/services');
      return services.getActiveTrip();
    },
    enabled,
    activeInterval: 8000, // 8 seconds
    backgroundInterval: 20000, // 20 seconds in background
  });
};

export const useRequestsPolling = (tripId?: string, enabled: boolean = true) => {
  return useSmartPolling({
    queryKey: ['requests', tripId],
    queryFn: async () => {
      if (!tripId) return null;
      const { services } = await import('@/services');
      return services.getRideRequests(tripId);
    },
    enabled: enabled && !!tripId,
    activeInterval: 7000, // 7 seconds for ride requests
    backgroundInterval: 25000, // 25 seconds in background
  });
};

export const useTripsPolling = (role: 'taken' | 'given', enabled: boolean = true) => {
  return useSmartPolling({
    queryKey: ['trips', role],
    queryFn: async () => {
      const { services } = await import('@/services');
      return services.getTrips(role);
    },
    enabled,
    activeInterval: 15000, // 15 seconds for trip lists
    backgroundInterval: 45000, // 45 seconds in background
  });
};

// Hook for detecting changes and triggering home page updates
export const useHomePagePolling = () => {
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  
  const activeTrip = useActiveTripPolling();
  const requests = useRequestsPolling(activeTrip.data?.id);
  
  // Detect changes and update timestamp
  useEffect(() => {
    if (activeTrip.dataUpdatedAt || requests.dataUpdatedAt) {
      setLastUpdate(new Date());
    }
  }, [activeTrip.dataUpdatedAt, requests.dataUpdatedAt]);

  return {
    activeTrip,
    requests,
    lastUpdate,
    isPolling: activeTrip.isPolling || requests.isPolling,
    hasChanges: activeTrip.dataUpdatedAt > Date.now() - 10000 || requests.dataUpdatedAt > Date.now() - 10000
  };
};
