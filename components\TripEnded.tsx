import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Image,
  Dimensions,
} from "react-native";
import React, { useMemo, useRef, useState } from "react";
import { router } from "expo-router";
import BottomSheet from "@gorhom/bottom-sheet";
import Modal from "@/components/Modal";
import Button from "./Button";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";
import { useUser } from "@/context/UserContext";
import { Trip } from "@/utils/types";

interface Props {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  trip: Trip;
}

const TripEnded: React.FC<Props> = ({ step, setStep, trip }) => {
  const [rating, setRating] = useState(0);
  const { ride } = useUser();

  const handleStarPress = (selectedRating: number) => {
    setRating(selectedRating);
    console.log("Selected rating:", selectedRating);
  };

  const renderStar = (starNumber: number) => {
    const starColor = starNumber <= rating ? "gold" : "gray";
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        key={starNumber}
        onPress={() => handleStarPress(starNumber)}
      >
        <Text style={[styles.star, { color: starColor }]}>★</Text>
      </TouchableOpacity>
    );
  };

  // Combined mutation for both payment and rating
  const { mutate: handleTripCompletion, isPending } = useMutation({
    mutationFn: async () => {
      const tripId = ride.rideId || trip.id;

      // Execute both API calls concurrently
      const [paymentResult, ratingResult] = await Promise.all([
        services.payForTrip({ dataBody: { tripId } }),
        services.rateTrip({
          dataBody: { rating },
          tripId,
        }),
      ]);

      return { paymentResult, ratingResult };
    },
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: "Trip paid and rated successfully",
      });
      setTimeout(() => {
        router.replace("/(drawer)/(tabs)/Home");
      }, 1000);
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response
          ? error.response.data.description || error.response.data.message
          : error.message,
      });
    },
  });

  const handleGoBack = () => {
    if (step === 1) {
      router.back();
    } else {
      setStep(step - 1);
    }
  };

  const bottomSheetRef = useRef<BottomSheet>(null);

  const screenHeight = Dimensions.get("window").height;
  const dynamicSnapPoint = useMemo(() => {
    const minHeight = 350;
    const percentHeight = screenHeight * 0.4;
    return Math.max(minHeight, percentHeight);
  }, [screenHeight]);

  const customSnapPoints = useMemo(
    () => [dynamicSnapPoint],
    [dynamicSnapPoint]
  );

  return (
    <Modal
      handleGoBack={handleGoBack}
      customSnapPoints={customSnapPoints}
      index={0}
      ref={bottomSheetRef}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        <View className="flex-1 w-[90%] mx-auto">
          <View className="justify-between flex-row py-4">
            <Text className="text-[#151B2D] font-semibold text-xl">
              Trip ended
            </Text>
            <TouchableOpacity
              activeOpacity={0.9}
              onPress={() => router.replace("/(tabs)/Home")}
            >
              <Image
                source={require("../assets/images/close_line.png")}
                className="w-[24px] h-[24px]"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>

          <View className="items-center mt-4">
            <Image
              source={require("../assets/images/ProfilePic.png")}
              className="w-[44px] h-[44px]"
              resizeMode="contain"
            />
            <Text className="text-[20px] text-[#151B2D] font-semibold">
              {`${trip?.driver?.firstName} ${trip?.driver?.lastName}`}
            </Text>
            <View className="flex-row gap-x-3 items-center">
              <Text className="text-[#151B2D] text-[15px]">
                {trip.driver.carDetails.make} {trip?.driver?.carDetails.model},{" "}
                {trip?.driver?.carDetails?.colour}
              </Text>
              <View className="bg-[#EAEAEA] rounded-[3px] p-[3px]">
                <Text className="text-[#151B2D] text-[13px] font-medium">
                  {trip?.driver.carDetails.plateNumber}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.container} className="mt-4">
            <View style={styles.starContainer}>
              {[1, 2, 3, 4, 5].map(renderStar)}
            </View>
          </View>
        </View>
        <View className="absolute bottom-[3%] left-0 right-0 px-5 pb-1 bg-white">
          <Button
            text="Pay & rate driver"
            buttonClassName="bg-[#473BF0] w-full mb-2"
            textClassName="text-white"
            onClick={handleTripCompletion}
            buttonDisabled={isPending}
            isLoading={isPending}
          />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
  },
  starContainer: {
    flexDirection: "row",
  },
  star: {
    fontSize: 25,
    marginHorizontal: 5,
  },
  ratingText: {
    marginTop: 10,
    fontSize: 18,
  },
});

export default TripEnded;
