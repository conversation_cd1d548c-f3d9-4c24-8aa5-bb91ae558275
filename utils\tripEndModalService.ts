import { pollingService } from './pollingService';

export interface TripEndModalData {
  tripId: string;
  tripDetails: {
    origin: string;
    destination: string;
    driverName?: string;
    passengerCount?: number;
    endTime: Date;
    duration?: string;
    distance?: string;
  };
  userRole: 'driver' | 'passenger';
  endReason: 'completed' | 'cancelled' | 'early';
  showRating?: boolean;
  showPayment?: boolean;
}

interface TripEndModalCallbacks {
  onModalShow?: (data: TripEndModalData) => void;
  onModalClose?: (tripId: string) => void;
  onRatingSubmit?: (tripId: string, rating: number) => void;
  onPaymentComplete?: (tripId: string) => void;
}

class TripEndModalService {
  private callbacks: TripEndModalCallbacks = {};
  private activeModals: Map<string, TripEndModalData> = new Map();

  // Register callbacks for modal events
  registerCallbacks(callbacks: TripEndModalCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Show trip end modal
  showTripEndModal(data: TripEndModalData) {
    console.log(`🎭 Showing trip end modal for trip ${data.tripId}`);
    
    this.activeModals.set(data.tripId, data);
    
    if (this.callbacks.onModalShow) {
      this.callbacks.onModalShow(data);
    }
  }

  // Close trip end modal
  closeTripEndModal(tripId: string) {
    console.log(`🎭 Closing trip end modal for trip ${tripId}`);
    
    this.activeModals.delete(tripId);
    
    if (this.callbacks.onModalClose) {
      this.callbacks.onModalClose(tripId);
    }
  }

  // Handle trip completion detected by polling
  handleTripCompleted(tripId: string, tripData: any, userRole: 'driver' | 'passenger') {
    const modalData: TripEndModalData = {
      tripId,
      tripDetails: {
        origin: tripData.origin?.name || 'Unknown',
        destination: tripData.destination?.name || 'Unknown',
        driverName: tripData.driver?.firstName ? 
          `${tripData.driver.firstName} ${tripData.driver.lastName || ''}`.trim() : 
          'Driver',
        passengerCount: tripData.passengers?.length || tripData.noOfPassengers || 0,
        endTime: new Date(),
        duration: this.calculateTripDuration(tripData.startTime),
        distance: tripData.distance || 'Unknown'
      },
      userRole,
      endReason: 'completed',
      showRating: userRole === 'passenger',
      showPayment: userRole === 'passenger'
    };

    this.showTripEndModal(modalData);
  }

  // Handle trip cancellation detected by polling
  handleTripCancelled(tripId: string, tripData: any, userRole: 'driver' | 'passenger', cancelledBy?: string) {
    const modalData: TripEndModalData = {
      tripId,
      tripDetails: {
        origin: tripData.origin?.name || 'Unknown',
        destination: tripData.destination?.name || 'Unknown',
        driverName: tripData.driver?.firstName ? 
          `${tripData.driver.firstName} ${tripData.driver.lastName || ''}`.trim() : 
          'Driver',
        passengerCount: tripData.passengers?.length || tripData.noOfPassengers || 0,
        endTime: new Date(),
      },
      userRole,
      endReason: 'cancelled',
      showRating: false,
      showPayment: false
    };

    this.showTripEndModal(modalData);
  }

  // Handle early trip end detected by polling
  handleTripEndedEarly(tripId: string, tripData: any, userRole: 'driver' | 'passenger', reason?: string) {
    const modalData: TripEndModalData = {
      tripId,
      tripDetails: {
        origin: tripData.origin?.name || 'Unknown',
        destination: tripData.destination?.name || 'Unknown',
        driverName: tripData.driver?.firstName ? 
          `${tripData.driver.firstName} ${tripData.driver.lastName || ''}`.trim() : 
          'Driver',
        passengerCount: tripData.passengers?.length || tripData.noOfPassengers || 0,
        endTime: new Date(),
        duration: this.calculateTripDuration(tripData.startTime),
      },
      userRole,
      endReason: 'early',
      showRating: userRole === 'passenger',
      showPayment: userRole === 'passenger'
    };

    this.showTripEndModal(modalData);
  }

  // Get active modal data
  getActiveModal(tripId: string): TripEndModalData | null {
    return this.activeModals.get(tripId) || null;
  }

  // Get all active modals
  getAllActiveModals(): TripEndModalData[] {
    return Array.from(this.activeModals.values());
  }

  // Check if modal is active for trip
  hasActiveModal(tripId: string): boolean {
    return this.activeModals.has(tripId);
  }

  // Handle rating submission
  submitRating(tripId: string, rating: number) {
    console.log(`⭐ Rating submitted for trip ${tripId}: ${rating} stars`);
    
    if (this.callbacks.onRatingSubmit) {
      this.callbacks.onRatingSubmit(tripId, rating);
    }
  }

  // Handle payment completion
  completePayment(tripId: string) {
    console.log(`💳 Payment completed for trip ${tripId}`);
    
    if (this.callbacks.onPaymentComplete) {
      this.callbacks.onPaymentComplete(tripId);
    }
    
    // Close modal after payment
    this.closeTripEndModal(tripId);
  }

  // Calculate trip duration
  private calculateTripDuration(startTime?: string): string {
    if (!startTime) return 'Unknown';
    
    const start = new Date(startTime);
    const end = new Date();
    const durationMs = end.getTime() - start.getTime();
    
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  // Clear all modals
  clearAllModals() {
    const tripIds = Array.from(this.activeModals.keys());
    tripIds.forEach(tripId => this.closeTripEndModal(tripId));
  }

  // Integration with polling service
  setupPollingIntegration() {
    // Register with polling service to detect trip status changes
    pollingService.registerChangeDetector('activeTrip', (newData, oldData) => {
      if (!newData || !oldData) return;
      
      // Detect trip completion
      if (oldData.status === 'ongoing' && newData.status === 'completed') {
        // Determine user role (you might need to adjust this logic)
        const userRole = this.determineUserRole(newData);
        this.handleTripCompleted(newData.id, newData, userRole);
      }
      
      // Detect trip cancellation
      if (oldData.status !== 'cancelled' && newData.status === 'cancelled') {
        const userRole = this.determineUserRole(newData);
        this.handleTripCancelled(newData.id, newData, userRole, newData.cancelledBy);
      }
    });

    console.log('🎭 Trip end modal service integrated with polling system');
  }

  // Determine user role based on trip data
  private determineUserRole(tripData: any): 'driver' | 'passenger' {
    // This logic might need to be adjusted based on your user context
    // For now, we'll use a simple heuristic
    if (tripData.driverId === tripData.currentUserId) {
      return 'driver';
    } else {
      return 'passenger';
    }
  }
}

// Export singleton instance
export const tripEndModalService = new TripEndModalService();

// Auto-setup polling integration
tripEndModalService.setupPollingIntegration();
