import { queryClient } from '@/providers';

interface PollingChangeDetector {
  lastData: any;
  onChange?: (newData: any, oldData: any) => void;
}

class PollingService {
  private changeDetectors: Map<string, PollingChangeDetector> = new Map();
  private homePageUpdateCallbacks: Set<() => void> = new Set();

  // Register a change detector for a specific query
  registerChangeDetector(queryKey: string, onChange?: (newData: any, oldData: any) => void) {
    this.changeDetectors.set(queryKey, {
      lastData: null,
      onChange
    });
  }

  // Check for changes in query data
  checkForChanges(queryKey: string, newData: any) {
    const detector = this.changeDetectors.get(queryKey);
    if (!detector) return false;

    const hasChanged = JSON.stringify(detector.lastData) !== JSON.stringify(newData);
    
    if (hasChanged && detector.onChange) {
      detector.onChange(newData, detector.lastData);
    }

    detector.lastData = newData;
    return hasChanged;
  }

  // Register callback for home page updates
  onHomePageUpdate(callback: () => void) {
    this.homePageUpdateCallbacks.add(callback);
    return () => this.homePageUpdateCallbacks.delete(callback);
  }

  // Trigger home page updates
  triggerHomePageUpdate() {
    this.homePageUpdateCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('Error in home page update callback:', error);
      }
    });
  }

  // Invalidate and refetch specific queries
  invalidateQueries(queryKeys: string[][]) {
    queryKeys.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
    });
  }

  // Force refresh all polling queries
  refreshAllPollingQueries() {
    const pollingQueries = [
      ['activeTrip'],
      ['requests'],
      ['trips', 'given'],
      ['trips', 'taken']
    ];

    this.invalidateQueries(pollingQueries);
    this.triggerHomePageUpdate();
  }

  // Detect trip status changes
  detectTripStatusChange(tripId: string, newStatus: string, oldStatus?: string) {
    if (oldStatus && newStatus !== oldStatus) {
      console.log(`🔄 Trip ${tripId} status changed: ${oldStatus} → ${newStatus}`);
      
      // Trigger specific actions based on status changes
      switch (newStatus) {
        case 'ongoing':
        case 'in_progress':
          this.onTripStarted(tripId);
          break;
        case 'completed':
          this.onTripCompleted(tripId);
          break;
        case 'cancelled':
          this.onTripCancelled(tripId);
          break;
      }

      this.triggerHomePageUpdate();
      return true;
    }
    return false;
  }

  // Trip event handlers
  private onTripStarted(tripId: string) {
    console.log(`🚗 Trip ${tripId} started - refreshing relevant queries`);
    this.invalidateQueries([
      ['activeTrip'],
      ['trip', tripId],
      ['requests', tripId]
    ]);
  }

  private onTripCompleted(tripId: string) {
    console.log(`✅ Trip ${tripId} completed - refreshing relevant queries`);
    this.invalidateQueries([
      ['activeTrip'],
      ['trip', tripId],
      ['trips', 'given'],
      ['trips', 'taken']
    ]);
  }

  private onTripCancelled(tripId: string) {
    console.log(`❌ Trip ${tripId} cancelled - refreshing relevant queries`);
    this.invalidateQueries([
      ['activeTrip'],
      ['trip', tripId],
      ['requests', tripId]
    ]);
  }

  // Detect new ride requests
  detectNewRideRequests(tripId: string, newRequests: any[], oldRequests?: any[]) {
    if (!oldRequests || !Array.isArray(newRequests) || !Array.isArray(oldRequests)) {
      return false;
    }

    const newRequestCount = newRequests.length;
    const oldRequestCount = oldRequests.length;

    if (newRequestCount > oldRequestCount) {
      console.log(`🔔 New ride request(s) for trip ${tripId}`);
      this.triggerHomePageUpdate();
      return true;
    }

    return false;
  }

  // Detect request status changes
  detectRequestStatusChanges(requests: any[], oldRequests?: any[]) {
    if (!oldRequests || !Array.isArray(requests) || !Array.isArray(oldRequests)) {
      return false;
    }

    let hasChanges = false;

    requests.forEach(request => {
      const oldRequest = oldRequests.find(old => old.id === request.id);
      if (oldRequest && oldRequest.status !== request.status) {
        console.log(`🔄 Request ${request.id} status changed: ${oldRequest.status} → ${request.status}`);
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.triggerHomePageUpdate();
    }

    return hasChanges;
  }

  // Clean up detectors
  cleanup() {
    this.changeDetectors.clear();
    this.homePageUpdateCallbacks.clear();
  }
}

// Export singleton instance
export const pollingService = new PollingService();

// Helper function to setup polling change detection
export const setupPollingChangeDetection = () => {
  // Register change detectors for common queries
  pollingService.registerChangeDetector('activeTrip', (newData, oldData) => {
    if (newData?.id && oldData?.id && newData.id === oldData.id) {
      pollingService.detectTripStatusChange(newData.id, newData.status, oldData.status);
    }
  });

  pollingService.registerChangeDetector('requests', (newData, oldData) => {
    if (newData?.data && oldData?.data) {
      pollingService.detectNewRideRequests(newData.tripId, newData.data, oldData.data);
      pollingService.detectRequestStatusChanges(newData.data, oldData.data);
    }
  });
};

// Auto-setup on import
setupPollingChangeDetection();
