import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import Button from './Button';
import { useTripStore } from '@/app/store/tripStore';
import { realtimeNotificationHelpers } from '@/utils/realtimeNotifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { services } from '@/services';
import Toast from 'react-native-toast-message';
import { useNotificationStore } from '@/app/store/notificationStore';
import { Realtime } from 'ably';
import { useRide } from '@/context/RideProvider';
import { useTripEndModals } from '@/hooks/useTripEndModals';
import CustomModal from './Modal';

interface HomeTripCardProps {
  trip: any;
  currentUser: any;
  arrivalTime?: Date;
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const tripDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  const isToday = tripDate.getTime() === today.getTime();
  const timeString = date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit', 
    hour12: true 
  });
  
  if (isToday) {
    return `Today at ${timeString}`;
  } else {
    return `${date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })} at ${timeString}`;
  }
};

const HomeTripCard: React.FC<HomeTripCardProps> = ({ trip, currentUser, arrivalTime }) => {
  const { tripStarted, tripProgress, setTripStarted, setTripProgress, resetTripState } = useTripStore();
  const { resetAllState } = useRide();
  const queryClient = useQueryClient();
  const { showModal } = useNotificationStore();

  // Determine user role
  const userRole = trip?.driverId === currentUser?.id ? 'driver' : 'passenger';

  // Trip end modals hook
  const tripEndModals = useTripEndModals(trip?.id, userRole);
  const ablyRef = useRef<Realtime | null>(null);

  // Countdown timer state
  const [countdown, setCountdown] = useState<string>('');
  const [isTimeToStart, setIsTimeToStart] = useState<boolean>(false);

  // Determine if current user is the driver
  const isDriver = currentUser?.id === trip?.driver?.id;

  // Determine trip status from API data
  const isTripStarted = trip?.status === 'ongoing' || trip?.status === 'in_progress' || tripStarted;
  const isTripCompleted = tripProgress >= 100;

  // Countdown timer logic
  useEffect(() => {
    if (!trip?.timestamp || isTripStarted) {
      setCountdown('');
      setIsTimeToStart(false);
      return;
    }

    const updateCountdown = () => {
      const now = new Date();
      const tripTime = new Date(trip.timestamp);

      const timeDiff = tripTime.getTime() - now.getTime();

      if (timeDiff <= 0) {
        // Time to start the trip
        setCountdown('');
        setIsTimeToStart(true);

        // Show modal to prompt user to start trip (only for drivers)
        if (!isTripStarted && isDriver) {
          // showModal({
          //   type: 'confirmation',
          //   title: '🚗 Time to Start Trip!',
          //   content: 'Your scheduled trip time has arrived. Are you ready to start the trip?',
          //   targetRole: 'driver',
          //   tripId: trip.id,
          //   buttons: [
          //     {
          //       text: 'Start Trip',
          //       action: 'custom',
          //       actionData: { callback: handleStartTrip },
          //       style: 'primary',
          //     },
          //     {
          //       text: 'Not Yet',
          //       action: 'close',
          //       style: 'default',
          //     },
          //   ],
          // });
        }
      } else {
        // Calculate countdown
        const hours = Math.floor(timeDiff / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        let countdownText = '';
        if (hours > 0) {
          countdownText = `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
          countdownText = `${minutes}m ${seconds}s`;
        } else {
          countdownText = `${seconds}s`;
        }

        setCountdown(countdownText);
        setIsTimeToStart(false);
      }
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [trip?.timestamp, isTripStarted, isDriver, showModal]);

  // Set up Ably subscription for progress updates (for passengers)
  useEffect(() => {
    if (!trip?.id || !currentUser?.id || isDriver) return;

    ablyRef.current = new Realtime({
      key: process.env.EXPO_PUBLIC_ABLY_KEY as string,
      clientId: currentUser.id,
      autoConnect: true,
    });

    const tripChannel = ablyRef.current.channels.get(`trip-${trip.id}`);

    // Subscribe to progress updates
    tripChannel.subscribe("progress-update", (message) => {
      if (message.data) {
        console.log('📱 HomeTripCard: Passenger received progress update:', message.data.progress + '%');
        setTripProgress(message.data.progress);
      }
    });

    return () => {
      tripChannel.unsubscribe();
      ablyRef.current?.close();
    };
  }, [trip?.id, currentUser?.id, isDriver, setTripProgress]);

  // Note: Start trip is handled locally without API call

  // Use the shared end trip logic from the hook
  const { isEndingTrip } = tripEndModals;

  // Cancel ride mutation (for passengers)
  const { mutate: cancelRide, isPending: isCancellingRide } = useMutation({
    mutationFn: services.cancelRideRequest,
    onSuccess: (data) => {
      Toast.show({
        type: "success",
        text1: data?.message || "Ride cancelled successfully",
      });

      queryClient.invalidateQueries({ queryKey: ["activeTrip"] });
      queryClient.invalidateQueries({ queryKey: ["requests"] });
      queryClient.invalidateQueries({ queryKey: ["trips"] });
    },
    onError: (error: any) => {
      Toast.show({
        type: "error",
        text1: error.response?.data?.description || "Failed to cancel ride",
      });
    },
  });

  const handleStartTrip = () => {
    // Check if there are passengers before starting the trip
    if (trip?.passengers?.length > 0) {
      console.log('🚀 HomeTripCard: Starting trip:', {
        tripId: trip?.id,
        tripStatus: trip?.status,
        currentTripStarted: tripStarted,
        isTripStarted
      });
      setTripStarted(true);
      Toast.show({
        type: "success",
        text1: "Your trip has started",
      });

      // Send realtime notification to passengers
      if (trip?.id && currentUser?.firstName) {
        // Pass complete trip data for proper navigation
        const completeTrip = {
          ...trip,
          driver: trip.driver || {
            id: currentUser.id,
            firstName: currentUser.firstName,
            lastName: currentUser.lastName,
            email: currentUser.email,
            phoneNumber: currentUser.phoneNumber,
            isVerified: currentUser.isVerified,
            carDetails: currentUser.carDetails
          },
          status: 'ongoing' // Mark as ongoing since trip is started
        };

        realtimeNotificationHelpers.notifyTripStarted(
          trip.id,
          currentUser.firstName,
          completeTrip // Pass complete trip object instead of subset
        );

        // Also publish direct event to trip channel (like driver notifications)
        // Note: We need access to ablyRef here, which might need to be passed as prop
        console.log('📤 HomeTripCard: Would publish trip-started to channel:', `trip-${trip.id}`);
      }
    } else {
      Toast.show({
        type: "error",
        text1: "Cannot start trip without passengers",
      });
    }
  };

  const handleEndTrip = () => {
    console.log('🔚 HomeTripCard: handleEndTrip called');
    console.log('🔚 Trip object:', trip);
    console.log('🔚 Trip ID:', trip?.id);
    console.log('🔚 User role:', userRole);
    tripEndModals.handleEndTripClick();
  };

  // Render trip end modals (only for drivers)
  const renderTripEndModals = () => {
    if (userRole !== 'driver') return null;

    return (
      <>
        {/* Main End Trip Modal */}
        {tripEndModals.showEndTripModal && (
          <CustomModal
            ref={tripEndModals.endTripModalRef}
            index={0}
            customSnapPoints={["30%"]}
          >
            <View className="flex-1 w-full relative bg-[#FFFFFF]">
              <View className="flex-1 items-center justify-center w-[90%] mx-auto">
                <Image
                  source={require("../assets/images/endTripNew.png")}
                  className='w-[40px] h-[40px] mb-4'
                />
                <Text className='font-semibold text-xl text-center mb-[6px]'>
                  End trip
                </Text>
                <Text className='text-[15px] font-normal text-center mb-6'>
                  Are you sure you want to end this trip?
                </Text>

                <View className="w-full space-y-3">
                  <TouchableOpacity
                    onPress={tripEndModals.handleTripCompletion}
                    disabled={isEndingTrip}
                    className={`w-full p-3 rounded-md ${isEndingTrip ? 'bg-gray-300' : 'bg-[#473BF0]'}`}
                  >
                    <Text className="text-white text-center font-medium">
                      {isEndingTrip ? 'Ending Trip...' : 'Yes, End Trip'}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => tripEndModals.setShowEndTripModal(false)}
                    className="w-full p-3 rounded-md bg-[#F4F4F4]"
                  >
                    <Text className="text-black text-center font-medium">
                      Cancel
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </CustomModal>
        )}

        {/* Passenger End Trip Modal */}
        {tripEndModals.showPassengerEndTripModal && (
          <CustomModal
            ref={tripEndModals.endTripModalRef}
            index={0}
            customSnapPoints={["30%"]}
          >
            <View className="flex-1 items-center justify-center w-[90%] mx-auto">
              <Text className='text-[40px] mb-[6px]'>🎉</Text>
              <Text className="text-xl font-semibold mb-4 text-center">
                Trip Completed!
              </Text>
              <Text className="text-center text-[15px] font-normal mb-6">
                You have successfully completed the trip!
              </Text>
              <TouchableOpacity
                onPress={() => {
                  tripEndModals.setShowPassengerEndTripModal(false);
                  router.replace("/(tabs)/Home");
                }}
                className="w-full p-3 rounded-md bg-[#473BF0]"
              >
                <Text className="text-white text-center font-medium">
                  Continue
                </Text>
              </TouchableOpacity>
            </View>
          </CustomModal>
        )}
      </>
    );
  };

  const handleCancelRide = () => {
    // Find the current user's passenger record to get their requestId
    const currentPassenger = trip?.passengers?.find((p: any) => p.id === currentUser?.id);
    if (currentPassenger?.requestId) {
      cancelRide({ dataBody: { requestId: currentPassenger.requestId } });
    } else {
      Toast.show({
        type: "error",
        text1: "Unable to cancel ride - request not found",
      });
    }
  };

  const handleExpandRide = () => {
    router.push({
      pathname: '/(ride)/TripSummary',
      params: { trip: JSON.stringify(trip) }
    });
  };

  const renderActionButtons = () => {
    if (isDriver) {
      return (
        <View className="flex-row mt-[16px] items-center">
          {!isTripStarted ? (
            <Button
              text={countdown ? `Start in ${countdown}` : isTimeToStart ? "Start Trip" : "Waiting..."}
              buttonClassName={`${isTimeToStart ? "bg-[#34A853]" : "bg-gray-400"} flex-1 mr-2`}
              textClassName="text-semibold text-[12px] text-white"
              onClick={handleStartTrip}
              buttonDisabled={!isTimeToStart}
              height="h-[30px]"
            />
          ) :  (
            <Button
              text="End Trip"
              buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
              textClassName="text-semibold text-[12px]"
              onClick={handleEndTrip}
              isLoading={isEndingTrip}
              buttonDisabled={isEndingTrip}
              height="h-[30px]"
            />
          )}

          <TouchableOpacity onPress={handleExpandRide} className='bg-[#473BF0] h-[30px] w-[93px] justify-center items-center rounded-full flex-row'>
            <Text className='text-white text-[12px] font-semibold'>Expand</Text>
            <Image source={require("../assets/images/right_line.png")} className='w-[15px] h-[15px] ml-1' tintColor="white" />

          </TouchableOpacity>

          {/* <Button
            text="Expand"
            buttonClassName="bg-[#473BF0]"
            textClassName="text-white text-[12px] font-semibold"
            width="w-[93px]"
            onClick={handleExpandRide}
            height="h-[30px]"
          /> */}
        </View>
      );
    } else {
      // Passenger view
      return (
        <View className="flex-row items-center mt-4">
          {isTripStarted && (
            <Button
              text="Cancel ride"
              buttonClassName="bg-[#F4F4F4] flex-1 mr-2"
              textClassName="text-black text-[12px]"
              onClick={handleCancelRide}
              isLoading={isCancellingRide}
              buttonDisabled={isCancellingRide}
              height="h-[30px]"
            />
          )}

          <TouchableOpacity onPress={handleExpandRide} className='bg-[#473BF0] h-[30px] w-[93px] justify-center items-center rounded-full flex-row'>
            <Text className='text-white text-[12px] font-semibold'>Expand</Text>
            <Image source={require("../assets/images/right_line.png")} className='w-[15px] h-[15px] ml-1' tintColor="white" />

          </TouchableOpacity>
        </View>
      );
    }
  };

  // Don't render if no trip data or missing essential properties
  if (!trip || !trip.origin || !trip.destination) return null;

  return (
    <View className=" mt-4">
      <View className="bg-white rounded-[6px] p-4 shadow-sm border border-gray-100">
        {/* Header */}
        <View className="flex-row items-center justify-between mb-[10px]">
          <View className='flex-row items-center'>
              <Image source={require("../assets/images/carr.png")} className='w-[15px] h-[15px] mr-1' />
              <Text className="font-medium text-[13px]">
                Coride
              </Text>
          </View>
          
          <View className="">
            <Text className="text-[13px] font-semibold">
              Now
            </Text>
          </View>
        </View>

        {/* Route Information */}
        <View className="">

          <View className='flex-row items-center'>
            <View className="items-center mr-1">
              <Image source={require("../assets/images/radioNew.png")} className="h-[10px] w-[10px]" />
              <View className="bg-[#B3B3B3] h-[14px] w-[1.5px] my-[1px]" />
              <Image source={require("../assets/images/blueLocationNew.png")} className="h-[12px] w-[12px]" />
            </View>
            <View className='flex-col flex-1'>
              <View className="mb-1">
                <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm">
                  <Text className='font-semibold'>
                    {trip?.origin?.name?.split(",")[0] || "Origin"}
                  </Text>
                  <Text className="font-normal text-[#787A80]">
                    {trip?.origin?.name?.split(",").slice(1).join(",") ? `, ${trip?.origin?.name?.split(",").slice(1).join(",")}` : ""}
                  </Text>
                </Text>
              </View>

              <View>
                <Text numberOfLines={1} ellipsizeMode="tail" className="text-sm">
                  <Text className='font-semibold'>
                    {trip?.destination?.name?.split(",")[0] || "Destination"}
                  </Text>
                  <Text className="font-normal text-[#787A80]">
                    {trip?.destination?.name?.split(",").slice(1).join(",") ? `, ${trip?.destination?.name?.split(",").slice(1).join(",")}` : ""}
                  </Text>
                </Text>
              </View>
            </View>
            
          </View>  
        
          
          {/* Progress Bar with Moving Car */}
          {isTripStarted && (
            <View className="mt-[16px]">
              <View className="relative">
                {/* Progress Bar Background */}
                <View className="h-[4px] bg-gray-200 rounded-full">
                  <View
                    className="h-[4px] bg-[#34A853] rounded-full"
                    style={{ width: `${tripProgress}%` }}
                  />
                </View>

                {/* Moving Car Marker */}
                <View
                  className="absolute -top-[6px] transform -translate-x-1/2"
                  style={{
                    left: `${Math.min(Math.max(tripProgress, 5), 95)}%`,
                  }}
                >
                  <Image
                    source={require("../assets/images/movingCar.png")}
                    className="w-[32px] h-[16px]"
                    resizeMode="contain"
                  />
                </View>
              </View>

              {/* <Text className="text-xs text-gray-500 mt-1 text-center">
                {tripProgress}% Complete
              </Text> */}
            </View>
          )}
        </View>

        {/* Action Buttons */}
        {renderActionButtons()}
      </View>

      {/* Trip End Modals - Only show for drivers */}
      {userRole === 'driver' && renderTripEndModals()}
    </View>
  );
};

export default HomeTripCard;
