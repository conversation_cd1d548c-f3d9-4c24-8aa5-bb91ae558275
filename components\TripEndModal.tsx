import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import CustomModal from './Modal';
import Button from './Button';
import { useTripEndModalIntegration } from '@/hooks/useTripEndModal';
import { router } from 'expo-router';

const TripEndModal: React.FC = () => {
  const {
    isVisible,
    modalRef,
    modalContent,
    rating,
    handleRatingChange,
    handleRatingSubmit,
    handlePaymentComplete,
    handleModalClose,
    canSubmitRating,
    tripDetails,
    isDriver,
    isPassenger
  } = useTripEndModalIntegration();

  if (!isVisible || !modalContent || !tripDetails) {
    return null;
  }

  const renderStars = () => {
    return (
      <View className="flex-row justify-center my-4">
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => handleRatingChange(star)}
            className="mx-1"
          >
            <Text className={`text-2xl ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}>
              ⭐
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderDriverContent = () => (
    <View className="flex-1 items-center justify-center w-[90%] mx-auto">
      <Text className="text-[40px] mb-[6px]">{modalContent.emoji}</Text>
      <Text className="text-xl font-semibold mb-4 text-center">
        {modalContent.title}
      </Text>
      <Text className="text-center text-[15px] font-normal mb-6">
        {modalContent.message}
      </Text>
      
      {/* Trip details */}
      <View className="w-full mb-6">
        <Text className="text-sm text-gray-600 text-center">
          {tripDetails.origin} → {tripDetails.destination}
        </Text>
        {tripDetails.duration && (
          <Text className="text-sm text-gray-600 text-center mt-1">
            Duration: {tripDetails.duration}
          </Text>
        )}
        {tripDetails.passengerCount && tripDetails.passengerCount > 0 && (
          <Text className="text-sm text-gray-600 text-center mt-1">
            Passengers: {tripDetails.passengerCount}
          </Text>
        )}
      </View>

      <Button
        text="Continue"
        buttonClassName="bg-[#473BF0] w-full mb-3"
        textClassName="text-white"
        onClick={() => {
          handleModalClose();
          router.replace("/(tabs)/Home");
        }}
      />
    </View>
  );

  const renderPassengerContent = () => (
    <View className="flex-1 w-[90%] mx-auto">
      {/* Header */}
      <View className="items-center py-4">
        <Text className="text-[40px] mb-[6px]">{modalContent.emoji}</Text>
        <Text className="text-xl font-semibold mb-4 text-center">
          {modalContent.title}
        </Text>
        <Text className="text-center text-[15px] font-normal mb-4">
          {modalContent.message}
        </Text>
      </View>

      {/* Trip details */}
      <View className="mb-4">
        <Text className="text-sm text-gray-600 text-center">
          {tripDetails.origin} → {tripDetails.destination}
        </Text>
        {tripDetails.driverName && (
          <Text className="text-sm text-gray-600 text-center mt-1">
            Driver: {tripDetails.driverName}
          </Text>
        )}
        {tripDetails.duration && (
          <Text className="text-sm text-gray-600 text-center mt-1">
            Duration: {tripDetails.duration}
          </Text>
        )}
      </View>

      {/* Rating section */}
      {modalContent.showRating && (
        <View className="mb-6">
          <Text className="text-center text-base font-medium mb-2">
            Rate your experience
          </Text>
          {renderStars()}
        </View>
      )}

      {/* Action buttons */}
      <View className="absolute bottom-[3%] left-0 right-0 px-5 pb-1 bg-white">
        {modalContent.showPayment && modalContent.showRating ? (
          <Button
            text="Pay & Rate Driver"
            buttonClassName={`w-full mb-2 ${canSubmitRating ? 'bg-[#473BF0]' : 'bg-gray-300'}`}
            textClassName="text-white"
            onClick={() => {
              if (canSubmitRating) {
                handleRatingSubmit();
                handlePaymentComplete();
              }
            }}
            buttonDisabled={!canSubmitRating}
          />
        ) : modalContent.showRating ? (
          <Button
            text="Submit Rating"
            buttonClassName={`w-full mb-2 ${canSubmitRating ? 'bg-[#473BF0]' : 'bg-gray-300'}`}
            textClassName="text-white"
            onClick={handleRatingSubmit}
            buttonDisabled={!canSubmitRating}
          />
        ) : (
          <Button
            text="Continue"
            buttonClassName="bg-[#473BF0] w-full mb-2"
            textClassName="text-white"
            onClick={() => {
              handleModalClose();
              router.replace("/(tabs)/Home");
            }}
          />
        )}
        
        <Button
          text="Close"
          buttonClassName="bg-[#F4F4F4] w-full"
          textClassName="text-black"
          onClick={() => {
            handleModalClose();
            router.replace("/(tabs)/Home");
          }}
        />
      </View>
    </View>
  );

  return (
    <CustomModal
      ref={modalRef}
      index={0}
      customSnapPoints={isPassenger && modalContent.showRating ? ["50%"] : ["35%"]}
    >
      <View className="flex-1 w-full relative bg-[#FFFFFF]">
        {isDriver ? renderDriverContent() : renderPassengerContent()}
      </View>
    </CustomModal>
  );
};

export default TripEndModal;
