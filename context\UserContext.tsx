import {
  createContext,
  Dispatch,
  ReactElement,
  ReactNode,
  SetStateAction,
  useContext,
  useState,
  useEffect,
  useRef,
} from "react";

import * as Location from "expo-location";
import * as Notifications from "expo-notifications";
import * as Device from "expo-device";
import Constants from "expo-constants";
import { Alert, Platform } from "react-native";
import { z } from "zod";
import useGetAblyToken from "@/hooks/useGetAblyToken";
import Ably from "ably";
import { RealtimeChannel } from "ably";
import { useMutation } from "@tanstack/react-query";
import { services } from "@/services";
import Toast from "react-native-toast-message";

interface UserInfo {
  gender: string;
  hasVehicle: any;
  vehicle: any;
  day: unknown;
  phoneNumber: string;
  email: string;
  ownPersonalVehicle: boolean;
  firstName: string;
  lastName: string;
  password: string;
  confirmPassword: string;
}

interface DeviceInfo {
  deviceId: string;
  deviceName: string;
  deviceModel: string;
}

interface Location {
  name: string;
  lat: number;
  lng: number;
}

interface CarpoolRide {
  rideId: string;
  modeOfPayment: string;
  pickup: Location;
  dropoff: Location;
  timestamp: string;
  userId: string;
  price: number;
  driverId: string;
  clientId: string;
}

interface Region {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

type userContextType = {
  displayCurrentAddress: string;
  setDisplayCurrentAddress: Dispatch<SetStateAction<string>>;
  locationServicesEnabled: boolean;
  setLocationServicesEnabled: Dispatch<SetStateAction<boolean>>;
  initialRegion: Region;
  setInitialRegion: Dispatch<SetStateAction<Region>>;
  getCurrentLocation: () => void;
  checkIfLocationEnabled: () => void;
  requestPermissionAgain: () => void;
  expoPushToken: string;
  deviceInfo: DeviceInfo;
  setUserDetails: (value: string | boolean, key: any) => void;
  userInfo: UserInfo;
  setUserInfo: Dispatch<SetStateAction<UserInfo>>;
  ride: CarpoolRide;
  updateLocation: (key: "pickup" | "dropoff", location: Location) => void;
  updateRide: (key: keyof CarpoolRide, value: any) => void;
  setRide: Dispatch<SetStateAction<CarpoolRide>>;
  resetUserRideState: () => void
};

const UserContext = createContext<userContextType | undefined>(undefined);

function useUser(): userContextType {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within an UserProvider");
  }
  return context;
}

const UserProvider = (props: { children: ReactNode }): ReactElement => {
  const [userInfo, setUserInfo] = useState({
    phoneNumber: "",
    email: "",
    ownPersonalVehicle: false,
    firstName: "",
    lastName: "",
    password: "",
    confirmPassword: "",
  });

  const { ablyToken } = useGetAblyToken();

  const initialUserRideState: CarpoolRide = {
    rideId: "",
    modeOfPayment: "",
    pickup: { name: "", lat: 0, lng: 0 },
    dropoff: { name: "", lat: 0, lng: 0 },
    timestamp: "",
    userId: "",
    price: 0,
    driverId: "",
    clientId: ablyToken?.clientId,
  };

  const [ride, setRide] = useState<CarpoolRide>(initialUserRideState);

  const updateLocation = (key: "pickup" | "dropoff", location: Location) => {
    setRide((prev) => ({ ...prev, [key]: location }));
  };

  const updateRide = (key: keyof CarpoolRide, value: any) => {
    setRide((prev) => ({ ...prev, [key]: value }));
  };

  const [displayCurrentAddress, setDisplayCurrentAddress] = useState(
    "Location Loading....."
  );
  const [locationServicesEnabled, setLocationServicesEnabled] = useState(false);
  const [initialRegion, setInitialRegion] = useState({
    latitude: 0,
    longitude: 0,
    latitudeDelta: 0,
    longitudeDelta: 0,
  });
  // console.log(initialRegion);
  const [expoPushToken, setExpoPushToken] = useState("");
  const [deviceInfo, setDeviceInfo] = useState({
    deviceId: "",
    deviceName: "",
    deviceModel: "",
  });

  useEffect(() => {
    checkIfLocationEnabled();
    // getDeviceInfo();
  }, []);

  const setUserDetails = (
    value: string | boolean,
    key: keyof typeof userInfo
  ) => {
    setUserInfo({ ...userInfo, [key]: value });
  };

  const fetchAddress = async (latitude: number, longitude: number) => {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${process.env.EXPO_PUBLIC_GOOGLE_API_KEY}`
    );

    const data = await response.json();
    if (data.results.length > 0) {
      return data.results[0];
    } else {
      return null;
    }
  };

  useEffect(() => {
    const getAddress = async () => {
      const address = await fetchAddress(
        initialRegion.latitude,
        initialRegion.longitude
      );
      // console.log(address); // This will now log the actual address result.
    };

    getAddress();
  }, [initialRegion]);

  const checkIfLocationEnabled = async () => {
    const enabled = await Location.hasServicesEnabledAsync();
    if (!enabled) {
      Alert.alert("Location not enabled", "Please enable your Location", [
        {
          text: "Cancel",
          style: "cancel",
        },
        { text: "OK", onPress: () => getCurrentLocation() },
      ]);
    } else {
      setLocationServicesEnabled(enabled);
      getCurrentLocation();
    }
  };

  const getCurrentLocation = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Please allow the app to use the location services",
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          { text: "OK", onPress: () => requestPermissionAgain() },
        ]
      );
      return;
    }

    const { coords } = await Location.getCurrentPositionAsync();
    if (coords) {
      const { latitude, longitude } = coords;

      setInitialRegion((prevRegion) => {
        if (
          prevRegion.latitude === latitude &&
          prevRegion.longitude === longitude
        ) {
          return prevRegion;
        }
        return {
          latitude,
          longitude,
          latitudeDelta: 0.005,
          longitudeDelta: 0.006,
        };
      });
      const address = await fetchAddress(latitude, longitude);

      if (address) {
        const addressString = address.formatted_address;
        setDisplayCurrentAddress(addressString);
      }
    }
  };

  const requestPermissionAgain = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status === "granted") {
      getCurrentLocation();
    }
  };

  const handleRegistrationError = (errorMessage: string) => {
    alert(errorMessage);
    throw new Error(errorMessage);
  };

  // const registerForPushNotificationsAsync = async () => {
  //   let token;

  //   if (Platform.OS === "android") {
  //     Notifications.setNotificationChannelAsync("default", {
  //       name: "default",
  //       importance: Notifications.AndroidImportance.MAX,
  //       vibrationPattern: [0, 250, 250, 250],
  //       lightColor: "#FF231F7C",
  //     });
  //   }

  //   if (Device.isDevice) {
  //     const { status: existingStatus } =
  //       await Notifications.getPermissionsAsync();
  //     let finalStatus = existingStatus;
  //     if (existingStatus !== "granted") {
  //       const { status } = await Notifications.requestPermissionsAsync();
  //       finalStatus = status;
  //     }
  //     if (finalStatus !== "granted") {
  //       handleRegistrationError(
  //         "Permission not granted to get push token for push notification!"
  //       );
  //       return;
  //     }
  //     const projectId = Constants?.expoConfig?.extra?.eas?.projectId;
  //     if (!projectId) {
  //       handleRegistrationError("Project ID not found");
  //     }
  //     token = await Notifications.getExpoPushTokenAsync({
  //       projectId: Constants.expoConfig!.extra!.eas.projectId,
  //     });

  //     return token.data;
  //   } else {
  //     console.log("Must use physical device for push notifications");
  //   }
  // };

  // const getDeviceInfo = async () => {
  //   const deviceId = Device.osBuildId || "unknown";
  //   const deviceName = Device.manufacturer || "unknown";
  //   const deviceModel = Device.modelName || "unknown";

  //   setDeviceInfo({
  //     deviceId,
  //     deviceName,
  //     deviceModel,
  //   });
  // };

  const resetUserRideState = () => {
    setRide(initialUserRideState);
  };

  return (
    <UserContext.Provider
      {...props}
      value={{
        displayCurrentAddress,
        setDisplayCurrentAddress,
        locationServicesEnabled,
        setLocationServicesEnabled,
        initialRegion,
        setInitialRegion,
        checkIfLocationEnabled,
        getCurrentLocation,
        requestPermissionAgain,
        expoPushToken,
        deviceInfo,
        setUserDetails,
        userInfo,
        setUserInfo,
        ride,
        updateLocation,
        updateRide,
        setRide,
        resetUserRideState
      }}
    />
  );
};

export { UserProvider, useUser };
